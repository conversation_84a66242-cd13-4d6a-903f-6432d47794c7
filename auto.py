import tkinter as tk
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import InvalidSessionIdException
from selenium.common.exceptions import NoSuchWindowException
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager



def run_browser():
    username = username_entry.get()
    password = password_entry.get()

    # Kiểm tra thông tin đăng nhập
    if not username or not password:
        print("<PERSON>ui lòng nhập đầy đủ username và password!")
        return

    # Khởi tạo trình duyệt
    service = Service(ChromeDriverManager().install())
    try:
        driver = webdriver.Chrome(service=service)
        driver.maximize_window()

        # URL trang web cần đăng nhập
        url = "https://web.sun.win/?affId=Sunwin"
        driver.get(url)

        # Tìm và điền thông tin đăng nhập
        username_input = driver.find_element(By.NAME, "Identity")
        password_input = driver.find_element(By.NAME, "Password")

        username_input.send_keys(username)
        password_input.send_keys(password)

        # Nhấp vào nút đăng nhập
        login_button = driver.find_element(By.XPATH, "//input[@type='submit']")
        login_button.click()

        # Đợi trang load sau khi đăng nhập thành công
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//ul[@class='header-menu-item' and @tabindex='2']"))
            )
            print("Đăng nhập thành công!")
        except TimeoutException:
            print("Đăng nhập thất bại hoặc trang load chậm!")

        # Giữ trình duyệt mở để người dùng có thể thao tác thủ công
        input("Đăng nhập hoàn tất - Nhấn Enter để đóng trình duyệt...")
        driver.quit()

    except InvalidSessionIdException:
       print("Trình duyệt đã bị đóng.")
    except NoSuchWindowException:
        print("Trình duyệt đã bị đóng.")
    except Exception as e:
        print(f"Có lỗi xảy ra: {str(e)}")
        if 'driver' in locals():
            driver.quit()
        




try:

    # Tạo cửa sổ chính
    root = tk.Tk()
    root.title("Login Tool")

    # Đặt kích thước cửa sổ (nhỏ hơn vì chỉ có login)
    window_width = 420
    window_height = 150

    # Lấy kích thước màn hình
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # Tính toán tọa độ trung tâm
    center_x = int(screen_width/2 - window_width / 2)
    center_y = int(screen_height/2 - window_height / 2)

    # Đặt kích thước và vị trí cho cửa sổ
    root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')
    root.resizable(False, False)

    # Tạo các trường nhập liệu cho login
    username_label = tk.Label(root, text="Username:")
    username_label.grid(row=1, column=0, padx=5, pady=5, sticky="e")
    username_entry = tk.Entry(root, width=40)
    username_entry.grid(row=1, column=1, padx=5, pady=5)

    password_label = tk.Label(root, text="Password:")
    password_label.grid(row=2, column=0, padx=5, pady=5, sticky="e")
    password_entry = tk.Entry(root, show="*", width=40)
    password_entry.grid(row=2, column=1, padx=5, pady=5)

    note_label = tk.Label(root, text="Nhập thông tin đăng nhập để truy cập website", fg="blue")
    note_label.grid(row=3, column=0, columnspan=2, padx=5, pady=5)

    # Tạo nút đăng nhập
    run_button = tk.Button(root, text="Đăng Nhập", command=run_browser, bg="#4CAF50", fg="white", font=("Arial", 10, "bold"))
    run_button.grid(row=4, column=0, columnspan=2, padx=5, pady=10)

    # Chạy giao diện
    root.mainloop()

except InvalidSessionIdException:
    print("Trình duyệt đã bị đóng.")
except NoSuchWindowException:
    print("Trình duyệt đã bị đóng.")