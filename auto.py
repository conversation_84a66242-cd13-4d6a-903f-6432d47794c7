import tkinter as tk
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import InvalidSessionIdException
from selenium.common.exceptions import NoSuchWindowException
from selenium.common.exceptions import NoSuchElementException
from selenium.common.exceptions import TimeoutException
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from webdriver_manager.chrome import ChromeDriverManager
from datetime import datetime
import time



def run_browser():
    # username = username_entry.get()
    # password = password_entry.get()
    # fromdate = fromdate_entry.get()
    # todate = todate_entry.get()

    username = "<EMAIL>"
    password = "Van0311**^"
    fromdate = "2024-01-01"
    todate = "2024-02-25"

    # Khởi tạo trình duyệt
    service = Service(ChromeDriverManager().install())
    try:
        driver = webdriver.Chrome(service=service)
        driver.maximize_window()

        # URL trang web cần đăng nhập
        url = "https://sale.hyundaihaiphong.vn/Dls_DealerCustomer?init=loadindex&fromdate=2019-01-01&todate=2025-03-10&dealercode=VN012&usercodeowner=ANHHTV%40HYUNDAIHAIPHONG.VN&CustomerBaseCode=&BusinessGroupCode=&custypecode=&cusgrouptype=&provincecode=&fullname=&phoneno=0395566386&phonenolh="
        driver.get(url)

        # Tìm và điền thông tin đăng nhập
        username_input = driver.find_element(By.NAME, "Identity")
        password_input = driver.find_element(By.NAME, "Password")

        username_input.send_keys(username)
        password_input.send_keys(password)

        # Nhấp vào nút đăng nhập
        login_button = driver.find_element(By.XPATH, "//input[@type='submit']")
        login_button.click()

        # doi chut hehe
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.XPATH, "//ul[@class='header-menu-item' and @tabindex='2']"))
        ) 

        #hovering nút CSKH
        CSKH_hover = driver.find_element(By.XPATH, "//ul[@class='header-menu-item' and @tabindex='2']")
        action = ActionChains(driver)
        action.move_to_element(CSKH_hover).perform()

        #vào 360
        nav_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//a[@href='/Dls_DealerCustomer']"))
        )
        nav_button.click()

        #chọn ngày
        start_date = driver.find_element(By.XPATH, "//input[@id='fromdate']")
        driver.execute_script("arguments[0].value = arguments[1];", start_date, fromdate)

        # #chọn đến ngày
        #today = datetime.now().strftime("%Y-%m-%d")

        end_date = driver.find_element(By.XPATH, "//input[@id='todate']")
        driver.execute_script("arguments[0].value = arguments[1];", end_date, todate)

        owner_dropdown = driver.find_element(By.XPATH, "//div[@id='s2id_usercodeowner']")
        owner_dropdown.click()

        owner_listbox = driver.find_elements(By.XPATH, "//ul[@id='select2-results-5']")   
        
        # print(owner_listbox)
        tatca = owner_listbox[0]
        # print(tatca)
        all_owner_option = tatca.find_element(By.XPATH, ".//div[@class='select2-result-label']")
        all_owner_option.click()

        #timkiem
        search_button = driver.find_element(By.XPATH, "//a[@onclick='SearchCT()']") 
        search_button.click()   
        time.sleep(3)


        #đây là lấy tổng số trang nhé Dũng
        try:
            panigation_div = driver.find_element(By.XPATH, "//div[@id='dynamic-table_paginate']")
            panigation_pull = WebDriverWait(panigation_div, 20).until(
                EC.presence_of_element_located((By.XPATH, ".//ul[@class='pagination pull-right']"))
            ) 
            panigation_li = panigation_pull.find_elements(By.TAG_NAME, "li")
            last_panigation = panigation_li[-2]
            last_num = int(last_panigation.text)
        except TimeoutException:
            last_num = 1
        print(last_num)


        #mẹ vừa không ghi comment đọc lại loạn vl
        for i in range(1, last_num + 1):
            print(i)
            #click từng khách hàng
            khachhang_tbody = WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.XPATH, "//tbody[@id='tbodyShowAllDealerCustomer']"))
            ) 
            rows = khachhang_tbody.find_elements(By.TAG_NAME, "tr")
            for row in rows:
                action = ActionChains(driver)
                action.double_click(row).perform()
                time.sleep(1.5)

                cols = row.find_elements(By.TAG_NAME, "td")
                sdt_num = cols[3].text

                #xử lý trong công việc
                all_tab = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, ".//ul[@id='myTab']")))
                working_tab = all_tab.find_element(By.XPATH, ".//li//a[@href='#infoDealerCustomerWorking']")
                working_tab.click()
                insert_button = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//div[@id='ButtonCreate']//h1//button[@id='button']")))
                insert_button.click()
                KPIcode_dropdown = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.XPATH, "//select[@id='KPIPlusCode']")))
                KPIcode_dropdown.click()
                KPIcode_option = WebDriverWait(KPIcode_dropdown, 10).until(EC.presence_of_element_located((By.XPATH, ".//option[@value='CV02']")))
                KPIcode_option.click()
                #tắt công việc (Sau đổi thành nút Lưu nhé Dũng)
                save_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, "//a[@onclick='CreateWkUserSchedule()']")))
                save_button.click()

                WebDriverWait(driver, 20).until(EC.alert_is_present())
                alert = driver.switch_to.alert
                print(f"Alert message: {alert.text}, sdt: {sdt_num}")
                alert.accept()  # Nhấn OK trên alert


                # Sau khi xử lý alert, tiếp tục với các hành động khác
                # Nếu vẫn cần đợi phần tử loading biến mất
                WebDriverWait(driver, 10).until(
                EC.invisibility_of_element_located((By.ID, "loading123_"))
                )


            #click sang trang tiếp
            try:
                panigation_div = driver.find_element(By.XPATH, "//div[@id='dynamic-table_paginate']")
                panigation_pull = WebDriverWait(panigation_div, 30).until(
                        EC.presence_of_element_located((By.XPATH, ".//ul[@class='pagination pull-right']"))
                ) 
                panigation_li = panigation_pull.find_elements(By.TAG_NAME, "li")
                next_panigation = panigation_li[-1].find_element(By.XPATH, ".//a[@class='next']")
                next_panigation.click()
            except TimeoutException:
                break
            #đợi tí
            time.sleep(3)
            


        # Đóng trình duyệt
        input("Đang giữ trình duyệt mở - Nhấn Enter để thoát...")
        driver.quit()
    except InvalidSessionIdException:
       print("Trình duyệt đã bị đóng.")
    except NoSuchWindowException:
        print("Trình duyệt đã bị đóng.")
        




try:

    # Tạo cửa sổ chính
    root = tk.Tk()
    root.title("MyAutomataTool")

    # Đặt kích thước cửa sổ
    window_width = 420
    window_height = 230

    # Lấy kích thước màn hình
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    # Tính toán tọa độ trung tâm
    center_x = int(screen_width/2 - window_width / 2)
    center_y = int(screen_height/2 - window_height / 2)

    # Đặt kích thước và vị trí cho cửa sổ
    root.geometry(f'{window_width}x{window_height}+{center_x}+{center_y}')
    root.resizable(False, False)

    # Tạo các trường nhập liệu nhé Dũng
    username_label = tk.Label(root, text="Username:")
    username_label.grid(row=1, column=0, padx=5, pady=5)
    username_entry = tk.Entry(root, width=50)
    username_entry.grid(row=1, column=1, padx=5, pady=5)

    password_label = tk.Label(root, text="Password:")
    password_label.grid(row=2, column=0, padx=5, pady=5)
    password_entry = tk.Entry(root, show="*", width=50)
    password_entry.grid(row=2, column=1, padx=5, pady=5)

    note_label = tk.Label(root, text="Note: Yêu cầu nhập đúng nếu không sẽ lỗi!", fg="red")
    note_label.grid(row=3, column=0, columnspan=2, padx=5, pady=5)

    fromdate_label = tk.Label(root, text="Ngày bắt đầu:")
    fromdate_label.grid(row=4, column=0, padx=5, pady=5)
    fromdate_entry = tk.Entry(root, width=50)
    fromdate_entry.grid(row=4, column=1, padx=5, pady=5)

    todate_label = tk.Label(root, text="Ngày kết thúc:")
    todate_label.grid(row=5, column=0, padx=5, pady=5)
    todate_entry = tk.Entry(root, width=50)
    todate_entry.grid(row=5, column=1, padx=5, pady=5)

    note_label = tk.Label(root, text="Nhập ngày tháng năm theo format yyyy-MM-dd", fg="blue")
    note_label.grid(row=6, column=0, columnspan=2, padx=5, pady=5)

    # Tạo nút chạy trình duyệt
    run_button = tk.Button(root, text="Chạy Trình Duyệt", command=run_browser)
    run_button.grid(row=7, column=0, columnspan=2, padx=5, pady=5)

    # Chạy giao diện
    root.mainloop()

except InvalidSessionIdException:
    print("Trình duyệt đã bị đóng.")
except NoSuchWindowException:
    print("Trình duyệt đã bị đóng.")